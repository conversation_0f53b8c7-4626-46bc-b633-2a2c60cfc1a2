import re
import gzip
import json
import logging
import datetime
import concurrent.futures
from io import BytesIO
from zoneinfo import ZoneInfo
from typing import Callable, Iterable, List, Tuple, Dict

import stripe
import requests
from bs4 import BeautifulSoup
import xml.etree.ElementTree as ET
from urllib.parse import urljoin, urlparse
from usp.tree import sitemap_tree_for_homepage, AbstractSitemap

from django.urls import reverse
from django.db import IntegrityError
from django.db.models import Case, When, Value, QuerySet

from AbunDRFBackend.settings import (REDIS_TASK_DATA_DB, REDIS_ART_GEN_EXPIRY, WEBSITE_SCANNING_MAX_WORKER, FLY_API_HOST,
                                     FLY_WEBSITE_SCANNING_APP_NAME, FLY_WEBSITE_SCANNING_DEPLOY_TOKEN, FLY_WEBSITE_SCANNING_GEN_IMAGE_URL,
                                     DEBUG)

from mainapp.chroma_db_manager import  ChromaDBManager
from mainapp.stripe_utils import get_stripe_product_data
from mainapp.quickindex.sitemaps import fetch_urls_from_sitemap_recursive
from mainapp.models import Website, WebPage, KubernetesJob, WebsiteScanQueue
from mainapp.utils import create_k8_job, generate_k8_job_id, chunker, get_redis_connection

# Set up logger
logger = logging.getLogger(__name__)

class WebsiteScanning:

    def __init__(self,
                 website: Website,
                 sitemaps: str | List = [],
                 website_urls:  List[Tuple[str, datetime.datetime | None]] = [],
                 stats: Dict | None = None,
                 k8_job_id: str | None = None,
                 available_machines: int = 150,
                 urls_to_process_per_machine: int = 100,
                 rescan: bool = False,
                 admin_request: bool = False,
                 website_scan_queue: None | WebsiteScanQueue = None) -> None:
        self.website = website
        self.user = website.user
        self.website_sitemaps = sitemaps
        self.website_stats = stats
        self.website_urls = website_urls
        self.rescan = rescan
        self.admin_request = admin_request
        self.website_filtered_urls: List[Tuple[str, str | None]] = []
        self.website_scanning_job_id: str | None = k8_job_id
        self.usp_tree: AbstractSitemap | None = None

        # for running tasks on fly.io
        self.available_machines = available_machines
        self.urls_to_process_per_machine = urls_to_process_per_machine
        self.website_scan_queue = website_scan_queue
        self.remaining_urls_to_process_per_machine: List | None = None
        self.machine_id: str | None = None
        self.machine_name: str | None = None

        if self.website_scanning_job_id:
            self.k8_job = KubernetesJob.objects.get(job_id=self.website_scanning_job_id)
        else:
            self.k8_job = None

    @staticmethod
    def process_urls_using_multiple_threads(fn: Callable, all_data: Iterable):
        """
        Process URLs using multiple threads.
        """
        # Use ThreadPoolExecutor to process URLs in parallel
        with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
            # Submit tasks to the executor for each URL
            futures = [executor.submit(fn, data) for data in all_data]

            # Wait for all tasks to complete
            for future in concurrent.futures.as_completed(futures):
                try:
                    yield future.result()
                except Exception as e:
                    logger.error(f"Error processing URL: {e}")

    @staticmethod
    def run_with_timeout(timeout: int, func: Callable, *args, **kwargs):
        """
        Executes a function with a predefined timeout.
        """
        with concurrent.futures.ThreadPoolExecutor() as executor:
            future = executor.submit(func, *args, **kwargs)
            try:
                return future.result(timeout=timeout)
            except concurrent.futures.TimeoutError:
                raise TimeoutError("Function execution timed out")

    def fetch_website_sitemaps_and_urls_using_robots__txt(self):
        """
        Extract all page URLs from a website's sitemaps using `robots.txt` file.
        """
        domain = self.website.domain
        base_url = f"https://{domain}"

        all_urls = set()  # Using a set for automatic deduplication
        processed_sitemaps = set()
        sitemap_urls = []

        # Create a session for connection pooling
        session = requests.Session()

        def check_sitemap_location(path: str):
            """
            Function to check if a sitemap exists at a specific path
            """
            try:
                sitemap_url = f"{base_url}{path}"
                response = session.head(sitemap_url, timeout=5)

                if response.status_code == 200:
                    return sitemap_url
                return None
            except requests.RequestException:
                return None

        def process_sitemap(sitemap_url: str):
            """
            Function to process a single sitemap
            """
            logger.info(f"Processing sitemap: {sitemap_url}")
            urls_found = []
            child_sitemaps = []

            try:
                # Handle gzipped sitemaps
                if sitemap_url.endswith('.gz'):
                    response = session.get(sitemap_url, timeout=10)
                    if response.status_code != 200:
                        return [], []

                    content = gzip.GzipFile(fileobj=BytesIO(response.content)).read()
                    sitemap_content = content.decode('utf-8')
                else:
                    response = session.get(sitemap_url, timeout=10)
                    if response.status_code != 200:
                        return [], []

                    sitemap_content = response.text

                # Check if it's an XML sitemap
                if '<?xml' in sitemap_content[:100]:
                    # Parse XML content
                    root = ET.fromstring(sitemap_content)

                    # Extract namespace if present
                    ns = ''
                    if root.tag.startswith('{'):
                        ns = root.tag.split('}')[0] + '}'

                    # Check if it's a sitemap index
                    is_sitemap_index = False

                    # Look for sitemap tags (indicating a sitemap index)
                    for sitemap_tag in root.findall(f'.//{ns}sitemap'):
                        is_sitemap_index = True
                        loc_tag = sitemap_tag.find(f'{ns}loc')
                        if loc_tag is not None and loc_tag.text:
                            child_sitemap_url = loc_tag.text.strip()
                            child_sitemaps.append(child_sitemap_url)

                    # If not a sitemap index, extract URLs
                    if not is_sitemap_index:
                        for url_tag in root.findall(f'.//{ns}url'):
                            loc_tag = url_tag.find(f'{ns}loc')
                            if loc_tag is not None and loc_tag.text:
                                page_url = loc_tag.text.strip()
                                urls_found.append(page_url)

                # Try to handle non-XML sitemaps (HTML or text)
                else:
                    # Check if it's HTML
                    if '<html' in sitemap_content.lower():
                        soup = BeautifulSoup(sitemap_content, 'html.parser')
                        for link in soup.find_all('a', href=True):
                            href = link['href']
                            full_url = urljoin(sitemap_url, href)
                            # Only include URLs from the same domain
                            if urlparse(full_url).netloc == domain:
                                urls_found.append(full_url)

                    # Assume it's a text sitemap with one URL per line
                    else:
                        for line in sitemap_content.splitlines():
                            line = line.strip()
                            if line.startswith('http'):
                                urls_found.append(line)

            except (requests.RequestException, ET.ParseError, UnicodeDecodeError, gzip.BadGzipFile) as e:
                logger.warning(f"Error processing sitemap {sitemap_url}: {e}")

            return urls_found, child_sitemaps

        def process_sitemaps_recursively(sitemap_list: List[str]):
            to_process = [url for url in sitemap_list if url not in processed_sitemaps]

            if not to_process:
                return []

            # Mark these as processed
            processed_sitemaps.update(to_process)

            new_sitemaps = []

            # Process this batch of sitemaps in parallel
            for result in self.process_urls_using_multiple_threads(process_sitemap, to_process):
                urls_found, child_sitemaps = result
                all_urls.update(urls_found)
                new_sitemaps.extend([url for url in child_sitemaps if url not in processed_sitemaps])

            # Process any child sitemaps we found
            if new_sitemaps:
                sitemap_urls.extend(new_sitemaps)
                process_sitemaps_recursively(new_sitemaps)

        # Check robots.txt first
        try:
            robots_url = f"{base_url}/robots.txt"
            response = session.get(robots_url, timeout=10)

            if response.status_code == 200:
                # Find sitemap directives in robots.txt
                for line in response.text.splitlines():
                    if re.match(r'(?i)^sitemap:', line):
                        sitemap_url = line.split(':', 1)[1].strip()
                        sitemap_urls.append(sitemap_url)

        except requests.RequestException as e:
            logger.warning(f"Error accessing robots.txt: {e}")

        # Try common locations
        common_sitemap_paths = [
            '/sitemap.xml',
            '/sitemap_index.xml',
            '/sitemap-index.xml',
            '/sitemapindex.xml',
            '/sitemap.php',
            '/sitemap.txt',
            '/sitemap.xml.gz'
        ]

        # Use multi-threading to check common sitemap locations
        for result in self.process_urls_using_multiple_threads(check_sitemap_location, common_sitemap_paths):
            if result:
                sitemap_urls.append(result)

        # Start the recursive processing
        process_sitemaps_recursively(sitemap_urls)

        # Store unique URLs & Sitemaps
        self.website_sitemaps = list(set(sitemap_urls))
        self.website_urls = [(url, None) for url in list(set(all_urls))]

    def fetch_website_sitemaps(self):
        """
        Find the website sitemaps
        """
        tree = sitemap_tree_for_homepage(f'https://{self.website.domain}')
        self.website_sitemaps = [sitemap.url for sitemap in tree.all_sitemaps()]
        self.usp_tree = tree

    def fetch_website_urls(self):
        """
        Find the website urls
        """
        if self.usp_tree:
            self.website_urls = [(page.url, page.last_modified) for page in self.usp_tree.all_pages()]
        else:
            # Fetch the URLs from provided sitemap URLs
            if isinstance(self.website_sitemaps, str):
                urls = fetch_urls_from_sitemap_recursive(self.website_sitemaps)
                self.website_urls.extend((url, None) for url in urls)
            else:
                for sitemap_url in self.website_sitemaps:
                    urls = fetch_urls_from_sitemap_recursive(sitemap_url)
                    self.website_urls.extend((url, None) for url in urls)

    def fetch_website_sitemaps_and_urls_using_usp(self):
        """
        Extract all page URLs from a website's sitemaps using `unlimited-sitemap-parser`.
        """
        tree = sitemap_tree_for_homepage(f'https://{self.website.domain}')
        self.website_sitemaps = [sitemap.url for sitemap in tree.all_sitemaps()]
        self.website_urls = [(page.url, page.last_modified) for page in tree.all_pages()]

    def fetch_website_sitemaps_and_urls(self):
        """
        Find the website sitemaps and all URLs using domain
        """
        if self.website_urls:
            logger.info("Website URLs are already fetched.")
            return None

        if self.website_sitemaps:
            # Fetch the URLs from provided sitemap URLs
            if isinstance(self.website_sitemaps, str):
                urls = fetch_urls_from_sitemap_recursive(self.website_sitemaps)
                self.website_urls.extend((url, None) for url in urls)
            else:
                for sitemap_url in self.website_sitemaps:
                    urls = fetch_urls_from_sitemap_recursive(sitemap_url)
                    self.website_urls.extend((url, None) for url in urls)
        else:
            # Fetch the website sitemaps and urls using robots.txt file
            self.fetch_website_sitemaps_and_urls_using_robots__txt()

            if not self.website_sitemaps or not self.website_urls:
                self.website_urls = []
                self.website_sitemaps = []

                # Fetch the website sitemaps and urls using usp
                self.run_with_timeout(80, self.fetch_website_sitemaps_and_urls_using_usp)

    def update_webpage(self, pages_data: List[Dict]):
        """
        Updates webpage details
        :param pages_data: Webpages data.
        """
        for data in pages_data:

            if data['status'] == "error":
                continue

            # Get the dtata
            scraped_data: Dict = data['data']

            url = scraped_data['url']
            title = scraped_data['title']
            summary = scraped_data['summary']
            schema = scraped_data['schema']
            schema_found = scraped_data.get('schema_found', 'no')  # Default to 'no' if not present
            last_modified = isinstance(scraped_data['last_modified'], str) and \
                    datetime.datetime.fromisoformat(scraped_data['last_modified']) or None

            try:
                webpage: WebPage = self.website.webpage_set.get(url=url)
            except WebPage.DoesNotExist:
                logger.error(f"No webpage exists for '{url}' for {self.website.domain} website.")
                return None

            webpage.url = url
            webpage.title = title
            webpage.summary = summary
            webpage.last_modified_on = last_modified
            webpage.last_scraped_on = datetime.datetime.now(tz=ZoneInfo("UTC"))
            webpage.schema = schema
            webpage.schema_found = schema_found.lower() == 'yes'  # Convert 'yes'/'no' to boolean
            webpage.save()

        self.k8_job.status = "completed"
        self.k8_job.save()

    def filter_urls(self):
        """
        Filters the website URLs.
        Removes already scraped urls & slice the list based on user plan
        """
        user = self.website.user

        try:
            current_plan = get_stripe_product_data(user)
            current_plan_name = current_plan['name']
        except stripe.error.InvalidRequestError:
            current_plan = None
            current_plan_name = 'Trial'

        # Remove already scraped URLs
        unique_urls = []
        urls_and_last_modified_date_mapping = {}

        for url, last_modified_date in self.website_urls:
            if url not in unique_urls:
                unique_urls.append(url)
                urls_and_last_modified_date_mapping[url] = last_modified_date

        # Remove already scraped URLs
        existing_urls = self.website.webpage_set.filter(url__in=unique_urls)
        existing_urls = list(existing_urls.values_list('url', flat=True))
        all_filtered_urls = [url for url in unique_urls if url not in existing_urls]

        # Slice the all_filtered_urls
        # - Scrape 50 pages if request is from admin panel
        # - If user is on 'Trial' plan then scrape 100 pages, else scrape 500 pages.
        slice_count = self.admin_request and 50 or (current_plan_name == 'Trial' and 100 or 500)
        filtered_urls = all_filtered_urls[:slice_count]

        if all_filtered_urls[slice_count:]:
            self.website.has_more_pages = True
        else:
            self.website.has_more_pages = False

        self.website.save()

        website_filtered_urls_and_last_modified_date_mapping = []

        for url in filtered_urls:
            if not urls_and_last_modified_date_mapping[url]:
                last_modified_date = None
            else:
                last_modified_date = urls_and_last_modified_date_mapping[url]

                if isinstance(last_modified_date, datetime.datetime):
                    last_modified_date = last_modified_date.isoformat()

            website_filtered_urls_and_last_modified_date_mapping.append((url, last_modified_date))

        self.website_filtered_urls = website_filtered_urls_and_last_modified_date_mapping

    def update_or_store_website_crawling_stats(self, initiallize_stats: bool = False):
        """
        Used to update or store website stats
        :param initiallize_stats: Initiallize the stats
        """
        with get_redis_connection(db=REDIS_TASK_DATA_DB) as redis_connection:
            stats_key = f"{self.website.domain}_{self.user.email}"

            if not self.website.task_queued or initiallize_stats:
                redis_connection.set(stats_key, json.dumps(self.website_stats))
            else:
                stats = redis_connection.get(stats_key)
                if not stats:
                    redis_connection.set(stats_key, json.dumps(self.website_stats))
                else:
                    json_stats = json.loads(stats)
                    json_stats["pages_scanned"] += self.website_stats["urls_processed"]
                    json_stats["progress"] = min(100, int((json_stats["pages_scanned"] / self.website_stats["total_pages"]) * 100))
                    json_stats["estimated_time_left"] = (self.website_stats["total_pages"] - json_stats["pages_scanned"]) * 7  # average estimate
                    redis_connection.set(stats_key, json.dumps(json_stats))

                    # Update the stats
                    self.website_stats = json_stats

            redis_connection.expire(stats_key, 21600)  # 6 hours


    def initialize_stats(self):
        """
        Used to initialize the stats
        """
        self.website_stats = {
            "total_pages": len(self.website_filtered_urls),
            "pages_scanned": 0,
            "progress": 0,
            "estimated_time_left": 0,
            "urls_processed": 0,
            "steps": {
                "crawling": "in_progress",
                "analyzing": "pending",
                "generating": "pending"
            }
        }

        self.update_or_store_website_crawling_stats(True)

    def store_website_pages_data(self, pages_data: List[Dict]):
        """
        Stores the scraped website pages data
        :param pages_data: Website crawled pages data
        """
        bulk_add_webpage = []

        for data in pages_data:

            if data['status'] == "error":
                continue

            # Get the dtata
            scraped_data: Dict = data['data']

            url = scraped_data['url']
            title = scraped_data['title']
            summary = scraped_data['summary']
            schema = scraped_data['schema']
            schema_found = scraped_data.get('schema_found', 'no')  # Default to 'no' if not present
            last_modified = isinstance(scraped_data['last_modified'], str) and \
                    datetime.datetime.fromisoformat(scraped_data['last_modified']) or None

            # Bulk add webpage
            bulk_add_webpage.append(
                WebPage(
                    website=self.website,
                    url=url,
                    title=title,
                    last_modified_on=last_modified,
                    last_scraped_on=datetime.datetime.now(tz=ZoneInfo("UTC")),
                    summary=summary,
                    schema=schema,
                    schema_found=schema_found.lower() == 'yes',  # Convert 'yes'/'no' to boolean
                    schema_enabled=True  # Default to enabled for new pages
                )
            )

        if bulk_add_webpage:
            try:
                webpages = WebPage.objects.bulk_create(bulk_add_webpage)
            except IntegrityError as e:
                # Handle the error as needed (log it, skip the duplicate, etc.)
                logger.error(f"Error while adding webpages in website_scanning_webhook() : {e}")
                return None

            # Bulk add to ChromaDB
            chromaDBManager = ChromaDBManager()
            webpage_map = chromaDBManager.bulk_add_pages(webpages)

            # Update embedding IDs
            case_statement = Case(
                *[When(id=webpage_id, then=Value(embedding_id)) for webpage_id, embedding_id in webpage_map.items()]
            )
            updated_count = WebPage.objects.filter(id__in=webpage_map.keys()).update(embedding_id=case_statement)

            # Verify all updates were successful
            if updated_count != len(webpage_map):
                logger.warning(f"Not all pages were updated. Expected {len(webpage_map)}, updated {updated_count}")

        # Update website crawling stats
        self.update_or_store_website_crawling_stats()

        # Check if all pages scraped
        if self.website_stats["progress"] == 100:
            # Mark the website as crawled
            self.website.is_crawled = True
            self.website.is_crawling = False
            self.website.task_queued = False
            self.website.is_failed = False
            self.website.crawling_ends_on = datetime.datetime.now(tz=ZoneInfo('UTC'))

            # Mark tasks as completed
            tasks: QuerySet[WebsiteScanQueue] = self.website.websitescanqueue_set.filter(status="processing")
            tasks.update(status="completed", completed_at=datetime.datetime.now(tz=ZoneInfo('UTC')))

            # Check if all pages were scanned
            if self.website_stats["pages_scanned"] != self.website_stats["total_pages"]:
                self.website.has_more_pages = True

            # Update crawling stats
            self.website_stats["steps"] = {
                "crawling": "completed",
                "analyzing": "completed",
                "generating": "completed",
            }
            self.update_or_store_website_crawling_stats()

            # Mark the k8 job as completed
            self.k8_job.status = "completed"
            self.k8_job.save()

            # Delete the redis key
            with get_redis_connection(db=REDIS_TASK_DATA_DB) as redis_connection:
                stats_key = f"{self.website.domain}_{self.user.email}"
                redis_connection.delete(stats_key)
                redis_connection.delete(self.website_scanning_job_id)

        self.website.save()

    def create_website_crawling_task(self):
        """
        Creates website crawling task based on environment
        """
        if DEBUG:
            self.create_website_crawling_task_on_k8s()
        else:
            self.create_website_crawling_task_on_flyio()

    def create_website_crawling_task_on_k8s(self):
        """
        Creates website crawling task on K8s
        """
        website_scanning_job_id = generate_k8_job_id('websitescanning', username=self.user.username)
        self.website_scanning_job_id = website_scanning_job_id

        website_scanning_data = {
            'domain': self.website.domain,
            'rescan': self.rescan,
            'article_language_preference' : self.website.article_language_preference,
            'chunk_size': WEBSITE_SCANNING_MAX_WORKER,
            'website_urls': self.website_filtered_urls,
            'abun_webhook_url': reverse('wh-k8-website-scanning'),
            'stats': self.website_stats,
        }

        with get_redis_connection(db=REDIS_TASK_DATA_DB) as redis_connection:
            redis_connection.set(self.website_scanning_job_id, json.dumps(website_scanning_data))
            redis_connection.expire(self.website_scanning_job_id, REDIS_ART_GEN_EXPIRY)

        website_scanning_gen_k8_job = KubernetesJob(
            job_id=self.website_scanning_job_id,
            user=self.user,
            status='running',
            metadata=self.website.domain,
        )
        website_scanning_gen_k8_job.save()
        self.k8_job = website_scanning_gen_k8_job

        create_k8_job(
            self.website_scanning_job_id,
            'website_scanning',
            self.website_scanning_job_id,
            self.user.id,
            [self.website_scanning_job_id]
        )

    def create_website_crawling_task_on_flyio(self):
        """
        Creates website crawling task on fly.io
        """
        if isinstance(self.remaining_urls_to_process_per_machine, List) and not self.remaining_urls_to_process_per_machine:
            raise RuntimeError("No URL is remaining to process.")

        if not self.remaining_urls_to_process_per_machine:
            urls_to_process_per_machine = list(chunker(self.website_filtered_urls, self.urls_to_process_per_machine))
            urls_to_process = urls_to_process_per_machine[:self.available_machines]

            # Store reamining urls
            self.remaining_urls_to_process_per_machine = urls_to_process_per_machine[self.available_machines:]

        else:
            urls_to_process = self.remaining_urls_to_process_per_machine[:self.available_machines]
            self.remaining_urls_to_process_per_machine = self.remaining_urls_to_process_per_machine[self.available_machines:]

        for urls_data in urls_to_process:
            websiter_scanning_job_id = generate_k8_job_id('websitescanning', username=self.user.username)
            self.website_scanning_job_id = websiter_scanning_job_id

            websiter_scanning_gen_k8_job = KubernetesJob(
                job_id=self.website_scanning_job_id,
                user=self.user,
                status='running',
                metadata=self.website.domain,
            )
            websiter_scanning_gen_k8_job.save()
            self.k8_job = websiter_scanning_gen_k8_job

            websiter_scanning_data = {
                'domain': self.website.domain,
                'rescan': self.rescan,
                'chunk_size': WEBSITE_SCANNING_MAX_WORKER,
                'article_language_preference' : self.website.article_language_preference,
                'website_urls': urls_data,
                'abun_webhook_url': reverse('wh-k8-website-scanning'),
                'stats': self.website_stats,
            }

            with get_redis_connection(db=REDIS_TASK_DATA_DB) as redis_connection:
                redis_connection.set(websiter_scanning_job_id, json.dumps(websiter_scanning_data))
                redis_connection.expire(websiter_scanning_job_id, REDIS_ART_GEN_EXPIRY)

            cmd = f"python3 batch_webpage_scraper.py {self.website_scanning_job_id}"
            cmd = cmd.split()
            worker_props = {
                "config": {
                    "image": FLY_WEBSITE_SCANNING_GEN_IMAGE_URL,
                    "auto_destroy": True,
                    "init": {
                        "cmd": cmd
                    },
                    "restart": {
                        "policy": "no"
                    },
                    "guest": {
                        "cpu_kind": "shared",
                        "cpus": 1,
                        "memory_mb": 1024
                    }
                },
            }

            res = requests.post(
                f"{FLY_API_HOST}/apps/{FLY_WEBSITE_SCANNING_APP_NAME}/machines",
                headers={
                    'Authorization': f"Bearer {FLY_WEBSITE_SCANNING_DEPLOY_TOKEN}",
                    'Content-Type': 'application/json'
                },
                json=worker_props
            )

            if res.status_code != 200:
                # Mark the k8 job as failed
                self.k8_job.status = "failed"
                self.k8_job.save()

                logger.error(res.text)
                raise Exception("Failed to send the website scanning link task to fly.io")

            # Get the machine ID and name
            machine_id: str = res.json()['id']
            machine_name: str = res.json()['name']

            # Store it for later use
            self.machine_id = machine_id
            self.machine_name = machine_name

            logger.debug(f"Machine ID: {self.machine_id}")
            logger.debug(f"Machine Name: {self.machine_name}")

            if not self.rescan:
                # Wait for the machine to enter the started state
                res = requests.get(
                    f"{FLY_API_HOST}/apps/{FLY_WEBSITE_SCANNING_APP_NAME}/machines/{machine_id}/wait?state=started",
                    headers={
                        'Authorization': f"Bearer {FLY_WEBSITE_SCANNING_DEPLOY_TOKEN}",
                        'Content-Type': 'application/json'
                    },
                    timeout=60
                )

                if res.status_code != 200:
                    # Set remaining URLs to process per machine as `None` to re-start the process
                    self.remaining_urls_to_process_per_machine = None
                    raise Exception(f"Bot was unable to enter 'started' state: {res.text}")

    def add_or_rescan_url(self, url: str):
        """
        Use this method to ad or rescan a URL
        """
        self.website_urls = [(url, None)]
        self.website_filtered_urls = [(url, None)]

        # Initialize stats
        self.initialize_stats()

        # Create tasks on K8s or Fly.io depending on the environment
        self.create_website_crawling_task()

    def continue_processing(self):
        """
        continue processing the URLs on fly.io
        """
        if DEBUG:
            raise Exception("Method is not allowed on dev/staging environment.")
        self.create_website_crawling_task_on_flyio()

    def __delete(self):
        """
        Use this method to delete the scraped data
        """
        # Fetch All the webpages
        webpages = WebPage.objects.filter(website=self.website)

        # Delete ChromaDB data
        chroma_db_ids = list(webpages.values_list('embedding_id', flat=True))

        if chroma_db_ids:
            chromaDBManager = ChromaDBManager()
            chromaDBManager.delete_pages(chroma_db_ids, self.website.id)

        # Delete WebPages Data
        webpages.delete()

        # Mark the website as not crawled
        self.website.is_crawled = False
        self.website.sitemap_urls = []
        self.website.save()

        # Delete redis key
        with get_redis_connection(db=REDIS_TASK_DATA_DB) as redis_connection:
            stats_key = f"{self.website.domain}_{self.user.email}"
            redis_connection.delete(stats_key)

    def delete_and_run(self):
        self.__delete()
        self.run()

    def run(self):
        # Fetch the website sitemaps & URLs
        self.website.finding_sitemaps = True
        self.website.save()

        if not self.website_sitemaps and not self.website_urls:
            self.fetch_website_sitemaps_and_urls()
        else:
            if not self.website_sitemaps:
                self.fetch_website_sitemaps()

            if not self.website_urls:
                self.fetch_website_urls()

        # Store the fetched details
        self.website.finding_sitemaps = False
        self.website.sitemap_urls = self.website_sitemaps
        self.website.save()

        # Filter the URLs
        self.filter_urls()

        if not self.website_filtered_urls:
            # Mark the website as crawled
            self.website.is_crawling = False
            self.website.is_failed = False
            self.website.task_queued = False
            self.website.has_more_pages = False
            self.website.is_crawled = True
            self.website.crawling_ends_on = datetime.datetime.now(tz=ZoneInfo('UTC'))
            self.website.save()

            # Update website scan queue
            if self.website_scan_queue:
                self.website_scan_queue.status = "completed"
                self.website_scan_queue.save()

            return None

        # Initialize stats
        self.initialize_stats()

        # Create tasks on K8s or Fly.io depending on the environment
        self.create_website_crawling_task()

        # Mark website as crawling
        self.website.crawling_started_on = datetime.datetime.now(tz=ZoneInfo('UTC'))
        self.website.is_crawling = True
        self.website.is_crawled = False
        self.website.is_failed = False
        self.website.save()
