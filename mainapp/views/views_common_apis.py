import os
import json
import base64
import hashlib
import logging
from typing import Dict

from django.core.handlers.wsgi import WSGIRequest
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.clickjacking import xframe_options_exempt
from django.http import JsonResponse, HttpResponse, StreamingHttpResponse

import openai

import tldextract
import cryptography.fernet
from rest_framework.request import Request
from rest_framework.decorators import api_view
from cryptography.fernet import Fe<PERSON><PERSON>, InvalidToken

from AbunDRFBackend import settings
from mainapp.utils import decrypt_dict
from mainapp.serializers import ChangeLogSerializer
from mainapp.json_responses import JsonResponseBadRequest
from mainapp.models import ChangeLog, AICalculator, AIStreamingToken, User, Website, WebPage


logger = logging.getLogger(__name__)

@api_view(['GET'])
def get_changelogs(request: Request):
    """
    Get Changelogs.

    :param request: Django Rest Framework's Request object.
    """
    return JsonResponse(
        status=200,
        data=ChangeLogSerializer(ChangeLog.objects.all().order_by("-created_at"), many=True).data,
        safe=False,
    )


@api_view(['GET'])
def get_embed_code_loading_script(request: Request):
    """
    API view to generate a customized JavaScript for loading embed script.
    :param request: Django Rest Framework's Request object.
    """
    try:
        # Read the template script file
        script_path = os.path.join(settings.BASE_DIR, 'tools_scripts', 'ai_calculator', 'embed_code_loading_script.js')
        
        # Verify file exists
        if not os.path.exists(script_path):
            logger.error(f"Script file not found at {script_path}")
            # Return JavaScript error rather than HTML
            return HttpResponse(
                "console.error('Script file not found on server');", 
                content_type='application/javascript'
            )
            
        with open(script_path, 'r') as file:
            script_content = file.read()

        # Replace placeholders securely
        server_url: str = request.build_absolute_uri('/')[:-1].replace('http://', 'https://')
        script_content = script_content.replace('SERVER_URL', f"{server_url}/api/frontend/get-calculator-embed-script/")

        # Set content type and additional headers
        response = HttpResponse(script_content, content_type='application/javascript')
        
        # Add cache-control and CORS headers
        response['Cache-Control'] = 'max-age=3600'
        
        # Set CORS headers to allow any domain to load this script
        response['Access-Control-Allow-Origin'] = '*'
        response['Access-Control-Allow-Methods'] = 'GET, OPTIONS'
        
        return response
        
    except Exception as e:
        # Log the error but return a JavaScript response
        logger.critical(f"Error serving script: {str(e)}")
        return HttpResponse(
            f"console.error('Error loading calculator script: {str(e)}');", 
            content_type='application/javascript'
        )


@api_view(['OPTIONS', 'GET'])
def get_calculator_embed_script(request: Request):
    """
    API view to generate a customized JavaScript embed script for a specific calculator.
    :param request: Django Rest Framework's Request object.
    """
    encrypted_data = request.query_params.get('data-calc', None)

    # Validate the request has required parameters
    if not encrypted_data:
        return JsonResponseBadRequest(additional_data={'err_id': "MISSING_PARAMETERS"})

    try:
        script_data = decrypt_dict(encrypted_data)
        calculator_id = script_data['CALCULATOR_ID']
        button_color = script_data.get('BUTTON_COLOR', '#007bff')
        website_name = script_data.get('WEBSITE_NAME', 'Website')
        calculator_title = script_data.get('CALCULATOR_TITLE', 'Calculator')
    except (KeyError, ValueError, cryptography.fernet.InvalidToken):
        return JsonResponseBadRequest(additional_data={'err_id': "INVALID_DATA"})

    try:
        ai_calculator = AICalculator.objects.get(calculator_id=calculator_id)
    except AICalculator.DoesNotExist:
        return JsonResponseBadRequest(additional_data={'err_id': "NO_CALCULATOR_FOUND"})

    # Read the template script file
    script_path = os.path.join(settings.BASE_DIR, 'tools_scripts', 'ai_calculator', 'calculator_embed.js')
    with open(script_path, 'r') as file:
        script_content = file.read()

    # Replace placeholders
    server_url = request.build_absolute_uri('/')[:-1].replace('http://', 'https://')
    script_content = script_content.replace('CALCULATOR_ID', calculator_id)
    script_content = script_content.replace('SERVER_URL', server_url)
    script_content = script_content.replace('BUTTON_COLOR', button_color)
    script_content = script_content.replace('WEBSITE_NAME', website_name)
    script_content = script_content.replace('CALCULATOR_TITLE', calculator_title)

    # Set up the response with the proper CORS headers - allow all origins
    response = HttpResponse(script_content, content_type='application/javascript')
    response['Access-Control-Allow-Origin'] = '*'
    response['Access-Control-Allow-Methods'] = 'GET, OPTIONS'
    response['Access-Control-Allow-Headers'] = 'Content-Type'
    response['Cache-Control'] = 'max-age=3600'

    return response


@api_view(['OPTIONS', 'GET'])
@xframe_options_exempt
def get_calculator_code(request: Request, calculator_id: str):
    """
    API view to get the calculator code
    :param request: Django Rest Framework's Request object.
    :param calculator_id: The unique ID of the calculator to retrieve
    """
    # Handle preflight OPTIONS request
    if request.method == 'OPTIONS':
        response = HttpResponse()
        response['Access-Control-Allow-Origin'] = '*'
        response['Access-Control-Allow-Methods'] = 'GET, OPTIONS'
        response['Access-Control-Allow-Headers'] = 'Content-Type'
        return response

    # Validate calculator_id format
    if not calculator_id or not isinstance(calculator_id, str) or len(calculator_id) > 100:
        logger.error(f"Invalid calculator ID format: {calculator_id}")
        return JsonResponseBadRequest(additional_data={'err_id': "INVALID_CALCULATOR_ID"})

    try:
        ai_calculator: AICalculator = AICalculator.objects.get(calculator_id=calculator_id)
    except AICalculator.DoesNotExist:
        logger.error(f"No calculator found with {calculator_id} calculator ID.")
        return JsonResponseBadRequest(additional_data={'err_id': "NO_CALCULATOR_FOUND"})

    # Get the html code
    html_code: str = ai_calculator.html_code

    # Create response with CORS headers
    response = HttpResponse(html_code, content_type="text/html")
    response['Access-Control-Allow-Origin'] = '*'
    response['Access-Control-Allow-Methods'] = 'GET, OPTIONS'
    response['Cache-Control'] = 'max-age=3600'  # Cache for 1 hour

    return response


@csrf_exempt
def stream_ai_response(request: WSGIRequest, token: str):
    """
    Streams the AI response
    :param request: Django Rest Framework's Request object
    :param token: Valid AI streaming token
    """
    if request.method == "POST":
        try:
            ai_streaming_token = AIStreamingToken.objects.get(token=token)
        except AIStreamingToken.DoesNotExist:
            return JsonResponseBadRequest(additional_data={"err_id": 'INVALID_TOKEN', "message": "Invalid token"})

        if not ai_streaming_token.is_valid():
            return JsonResponseBadRequest(additional_data={"err_id": 'EXPIRED_TOKEN', "message": "Token expired or invalid"})

        # get the payload from the body
        payload: Dict = json.loads(request.body.decode())

        # update the prompt if its a translation request
        translation_matching_prompt = """
        请帮我翻译以上内容，在翻译之前，想先判断一下这个内容是不是中文，如果是中文，则翻译问英文，如果是其他语言，则需要翻译为中文，注意，你只需要返回翻译的结果，不需要对此进行任何解释，不需要除了翻译结果以外的其他任何内容
        """.replace("\n", "").strip()

        updated_prompt = """
        Please help me translate the above content. Before translating, I want to determine whether the content is in English (US). If it is in English (US), translate it into English (UK). If it is in other languages, translate it into English (US). Please note that you only need to return the translation result, and you do not need to explain it or any other content except the translation result.
        """.replace("\n", "").strip()

        if translation_matching_prompt in payload['messages'][0]['content']:
            payload['messages'][0]['content'] = payload['messages'][0]['content'].replace(translation_matching_prompt, updated_prompt)

        def generate():
            client = openai.Client()
            try:
                # Create the streaming completion with the new API
                stream = client.chat.completions.create(**payload)
            except openai._exceptions.InternalServerError:
                return JsonResponseBadRequest(additional_data={"err_id": 'SERVER_ERROR', "message": "Server error"})

            for chunk in stream:
                if chunk.choices[0].delta.content is not None:
                    yield f"data: {json.dumps({'choices': [{'delta': {'content': chunk.choices[0].delta.content}}]})}\n\n"

        # mark the token as used
        ai_streaming_token.used = True
        ai_streaming_token.save()

        return StreamingHttpResponse(generate(), content_type="text/event-stream")

    return JsonResponseBadRequest(additional_data={"err_id": 'METHOD_NOT_ALLOWED', "message": "Requested method is not allowed"})


@api_view(['OPTIONS', 'GET'])
def get_tools_loading_script(request: Request):
    """
    API view to generate a customized JavaScript for loading all tools.
    :param request: Django Rest Framework's Request object.
    """
    try:
        # Read the template script file
        script_path = os.path.join(settings.BASE_DIR, 'tools_scripts', 'get_all_tools_loading_script.js')
        
        # Verify file exists
        if not os.path.exists(script_path):
            logger.error(f"Script file not found at {script_path}")
            # Return JavaScript error rather than HTML
            return HttpResponse(
                "console.error('Script file not found on server');", 
                content_type='application/javascript'
            )
            
        with open(script_path, 'r') as file:
            script_content = file.read()

        # Replace placeholders securely
        server_url: str = request.build_absolute_uri('/')[:-1].replace('http://', 'https://')
        script_content = script_content.replace('SERVER_URL', f"{server_url}/api/frontend/load-tools-scripts/")

        # Set content type and additional headers
        response = HttpResponse(script_content, content_type='application/javascript')
        
        # Add cache-control and CORS headers
        response['Cache-Control'] = 'max-age=3600'
        
        # Set CORS headers to allow any domain to load this script
        response['Access-Control-Allow-Origin'] = '*'
        response['Access-Control-Allow-Methods'] = 'GET, OPTIONS'
        
        return response
        
    except Exception as e:
        # Log the error but return a JavaScript response
        logger.critical(f"Error serving script: {str(e)}")
        return HttpResponse(
            f"console.error('Error loading calculator script: {str(e)}');", 
            content_type='application/javascript'
        )


@api_view(['OPTIONS', 'GET'])
def load_tools_scripts(request: Request):
    """
    API view to generate a customized JavaScript for loading all tools.
    :param request: Django Rest Framework's Request object.
    """
    try:
        encrpted_user_email: str = request.query_params["user-id"]
        page_url: str = request.query_params["url"]
    except KeyError:
        return JsonResponseBadRequest(additional_data={'err_id': "MISSING_PARAMETERS"})

    # Decrypt the user email
    key = base64.urlsafe_b64encode(hashlib.sha256(settings.SECRET_KEY.encode()).digest())
    f = Fernet(key)

    # Decrypt the user email
    try:
        user_email = f.decrypt(encrpted_user_email.encode()).decode()
    except InvalidToken:
        logger.error(f"Invalid user email token: {user_email}")
        return JsonResponseBadRequest(additional_data={'err_id': "INVALID_USER_EMAIL"})
    except Exception as e:
        logger.critical(f"Error decrypting user email: {str(e)}")
        return JsonResponseBadRequest(additional_data={'err_id': "INVALID_USER_EMAIL"})

    # Get the user
    try:
        user: User = User.objects.get(email=user_email)
    except User.DoesNotExist:
        logger.error(f"No user found with {user_email} email.")
        return JsonResponseBadRequest(additional_data={'err_id': "NO_USER_FOUND"})

    # Extract the domain from the page url
    domain_extract: str = tldextract.extract(page_url)

    if domain_extract.subdomain:
        domain: str = f"{domain_extract.subdomain}.{domain_extract.domain}.{domain_extract.suffix}"
    else:
        domain: str = f"{domain_extract.registered_domain}"

    # Get the website
    try:
        website: Website = user.website_set.get(domain=domain)
    except Website.DoesNotExist:
        logger.error(f"No website found with {domain} domain.")
        return JsonResponseBadRequest(additional_data={'err_id': "NO_WEBSITE_FOUND"})

    # List to store all script contents
    script_contents = []

    # Auto Schema Tool - load if website has webpages
    # Get the webpage
    try:
        webpage: WebPage = website.webpage_set.get(url=page_url)
    except WebPage.DoesNotExist:
        logger.error(f"No webpage found with {page_url} url.")
        webpage = None

    if webpage and webpage.schema_enabled:
        try:
            auto_schema_script_path = os.path.join(settings.BASE_DIR, 'tools_scripts', 'auto_schema', 'auto_schema_tool.js')

            if os.path.exists(auto_schema_script_path):
                with open(auto_schema_script_path, 'r') as file:
                    auto_schema_content = file.read()

                    # Convert schema to string
                    schema_string = json.dumps(webpage.schema)

                    # Replace placeholders
                    auto_schema_content = auto_schema_content.replace("'JSONLD_SCHEMA'", schema_string)
                    script_contents.append(auto_schema_content)

            else:
                logger.error(f"Auto schema script not found at {auto_schema_script_path}")

        except Exception as e:
            logger.critical(f"Error loading auto schema script: {str(e)}")
    
    # Future tools can be added here with similar pattern:
    # Example:
    # if website.some_other_tool_enabled:
    #     try:
    #         other_tool_script_path = os.path.join(settings.BASE_DIR, 'tools_scripts', 'other_tool', 'other_tool.js')
    #         if os.path.exists(other_tool_script_path):
    #             with open(other_tool_script_path, 'r') as file:
    #                 other_tool_content = file.read()
    #                 other_tool_content = other_tool_content.replace('SERVER_URL', server_url)
    #                 script_contents.append(other_tool_content)
    #     except Exception as e:
    #         logger.error(f"Error loading other tool script: {str(e)}")
    
    # Combine all scripts
    if script_contents:
        combined_script = '\n\n'.join(script_contents)

        # Tools loading script added
        if not website.tools_loading_script_added:
            website.tools_loading_script_added = True
            website.save()

    else:
        # Return empty script if no tools are active
        combined_script = '// No tools are currently active for this website'
    
    # Set up the response with proper headers
    response = HttpResponse(combined_script, content_type='application/javascript')
    response['Cache-Control'] = 'max-age=3600'
    response['Access-Control-Allow-Origin'] = '*'
    response['Access-Control-Allow-Methods'] = 'GET, OPTIONS'
    
    return response
